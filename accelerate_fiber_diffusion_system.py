import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from diffusers import UNet2DModel, DDPMScheduler
from accelerate import Accelerator
from accelerate.utils import set_seed
import numpy as np
import math
import os
import glob
from PIL import Image
from torchvision import transforms
from tqdm import tqdm
import logging
from torch.optim.lr_scheduler import CosineAnnealingLR
import argparse
from typing import Optional

# Try to import ProjectConfiguration for newer versions
try:
    from accelerate import ProjectConfiguration
    HAS_PROJECT_CONFIG = True
except ImportError:
    HAS_PROJECT_CONFIG = False
    ProjectConfiguration = None

# Check accelerate version compatibility
def check_accelerate_version():
    """Check if accelerate version is compatible"""
    import accelerate
    
    try:
        # Parse version to check if it's new enough
        version_parts = accelerate.__version__.split('.')
        major, minor = int(version_parts[0]), int(version_parts[1])
        
        # Version 1.9.0 is definitely new enough, any version >= 0.16.0 should be fine
        if major >= 1 or (major == 0 and minor >= 16):
            return True
        else:
            print(f"⚠️  Warning: You're using accelerate {accelerate.__version__}.")
            print(f"   Some features require accelerate >= 0.16.0")
            print(f"   Consider upgrading: pip install --upgrade accelerate")
            return False
    except (ValueError, IndexError):
        # If version parsing fails, just show the version
        print(f"ℹ️  Using accelerate version: {accelerate.__version__}")
        return True

# Run version check
ACCELERATE_VERSION_OK = check_accelerate_version()


# ═══════════════════════════════════════════════════════════════════
# ENHANCED DIRECTIONAL COMPONENTS FOR CURVED AND LINEAR FIBERS
# ═══════════════════════════════════════════════════════════════════

class CurvedDirectionalBlock(nn.Module):
    """Enhanced directional block that handles both LINEAR and CURVED fiber patterns"""
    
    def __init__(self, channels):
        super().__init__()
        
        # Calculate appropriate output channels for each filter
        # Ensure we have at least 1 channel per filter and total is reasonable
        if channels <= 3:
            # For small channel counts, use fixed output channels
            out_channels_per_filter = 4
            total_out_channels = 8 * out_channels_per_filter  # 32 channels total
        else:
            # For larger channel counts, use proportional division
            out_channels_per_filter = max(1, channels // 8)
            total_out_channels = 8 * out_channels_per_filter
        
        # Linear directional filters
        self.conv_horizontal = nn.Conv2d(channels, out_channels_per_filter, (1, 7), padding=(0, 3))
        self.conv_vertical = nn.Conv2d(channels, out_channels_per_filter, (7, 1), padding=(3, 0))
        self.conv_diag_45 = nn.Conv2d(channels, out_channels_per_filter, (5, 5), padding=2)
        self.conv_diag_135 = nn.Conv2d(channels, out_channels_per_filter, (5, 5), padding=2)
        
        # Circular/curved filters
        self.conv_circle_small = nn.Conv2d(channels, out_channels_per_filter, (7, 7), padding=3)
        self.conv_circle_medium = nn.Conv2d(channels, out_channels_per_filter, (11, 11), padding=5)
        self.conv_circle_large = nn.Conv2d(channels, out_channels_per_filter, (15, 15), padding=7)
        self.conv_arc = nn.Conv2d(channels, out_channels_per_filter, (9, 9), padding=4)
        
        # Normalization and activation with proper group count
        # Ensure num_groups divides total_out_channels
        if total_out_channels >= 32:
            num_groups = 8
        elif total_out_channels >= 16:
            num_groups = 4
        elif total_out_channels >= 8:
            num_groups = 2
        else:
            num_groups = 1
            
        self.norm = nn.GroupNorm(num_groups, total_out_channels)
        self.activation = nn.SiLU()
        
        # Store for later use
        self.out_channels_per_filter = out_channels_per_filter
        self.total_out_channels = total_out_channels
        
        # Initialize filters with proper patterns
        self._init_filters()
        
    def _init_filters(self):
        """Initialize all directional filters with appropriate patterns"""
        with torch.no_grad():
            # Initialize linear diagonal filters
            self._init_diagonal_filter(self.conv_diag_45, True)   # 45° diagonal
            self._init_diagonal_filter(self.conv_diag_135, False) # 135° diagonal
            
            # Initialize circular filters
            self._init_circular_filter(self.conv_circle_small, 2.0)
            self._init_circular_filter(self.conv_circle_medium, 4.0)
            self._init_circular_filter(self.conv_circle_large, 6.0)
            self._init_arc_filter(self.conv_arc, 3.0)
    
    def _init_diagonal_filter(self, conv_layer, is_45_degree):
        """Initialize diagonal filters"""
        size = conv_layer.weight.shape[-1]
        mask = torch.zeros(size, size)
        
        for i in range(size):
            for j in range(size):
                if is_45_degree:
                    if abs(i - j) <= 1:  # Main diagonal ±1
                        mask[i, j] = 1.0
                else:
                    if abs(i + j - (size - 1)) <= 1:  # Anti-diagonal ±1
                        mask[i, j] = 1.0
        
        # Apply mask to all channels
        for out_ch in range(conv_layer.weight.shape[0]):
            for in_ch in range(conv_layer.weight.shape[1]):
                conv_layer.weight.data[out_ch, in_ch] *= mask
    
    def _init_circular_filter(self, conv_layer, radius):
        """Initialize circular filters"""
        size = conv_layer.weight.shape[-1]
        center = size // 2
        mask = torch.zeros(size, size)
        
        for i in range(size):
            for j in range(size):
                dist = math.sqrt((i - center)**2 + (j - center)**2)
                if abs(dist - radius) <= 0.8:  # Ring pattern
                    mask[i, j] = 1.0
        
        # Apply mask to all channels
        for out_ch in range(conv_layer.weight.shape[0]):
            for in_ch in range(conv_layer.weight.shape[1]):
                conv_layer.weight.data[out_ch, in_ch] *= mask
    
    def _init_arc_filter(self, conv_layer, radius):
        """Initialize arc filters for partial circles"""
        size = conv_layer.weight.shape[-1]
        center = size // 2
        mask = torch.zeros(size, size)
        
        for i in range(size):
            for j in range(size):
                dist = math.sqrt((i - center)**2 + (j - center)**2)
                angle = math.atan2(i - center, j - center)
                
                # Half circle pattern
                if abs(dist - radius) <= 0.8 and 0 <= angle <= math.pi:
                    mask[i, j] = 1.0
        
        # Apply mask to all channels
        for out_ch in range(conv_layer.weight.shape[0]):
            for in_ch in range(conv_layer.weight.shape[1]):
                conv_layer.weight.data[out_ch, in_ch] *= mask
    
    def forward(self, x):
        """Apply all directional filters and combine results"""
        features = []
        
        # Linear features
        features.append(self.conv_horizontal(x))
        features.append(self.conv_vertical(x))
        features.append(self.conv_diag_45(x))
        features.append(self.conv_diag_135(x))
        
        # Curved features
        features.append(self.conv_circle_small(x))
        features.append(self.conv_circle_medium(x))
        features.append(self.conv_circle_large(x))
        features.append(self.conv_arc(x))
        
        # Concatenate all features
        all_features = torch.cat(features, dim=1)
        
        # Normalize and activate
        return self.activation(self.norm(all_features))


class EnhancedFiberPreprocessor(nn.Module):
    """Enhanced preprocessor that handles both linear and curved fibers"""
    
    def __init__(self, in_channels=3):
        super().__init__()
        
        self.curved_directional = CurvedDirectionalBlock(in_channels)
        
        # Get the actual output channels from the directional block
        directional_out_channels = self.curved_directional.total_out_channels
        
        # Fusion layer - combine original + directional features
        fusion_in_channels = in_channels + directional_out_channels
        self.fusion = nn.Conv2d(fusion_in_channels, in_channels, 1)
        self.fusion_norm = nn.GroupNorm(1, in_channels)
        self.fusion_activation = nn.SiLU()
        
        # Proper weight initialization
        self._init_weights()
        
    def _init_weights(self):
        """Initialize weights using Xavier initialization"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.GroupNorm):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, x):
        # Extract enhanced directional features
        directional_features = self.curved_directional(x)
        
        # Combine original + directional features
        combined = torch.cat([x, directional_features], dim=1)
        
        # Fuse back to original dimensions
        fused = self.fusion(combined)
        fused = self.fusion_norm(fused)
        fused = self.fusion_activation(fused)
        
        return fused


# ═══════════════════════════════════════════════════════════════════
# ENHANCED UNET WITH DIRECTIONAL PREPROCESSING
# ═══════════════════════════════════════════════════════════════════

class EnhancedFiberUNet(nn.Module):
    """Complete UNet with enhanced directional preprocessing for fiber networks"""
    
    def __init__(self, 
                 sample_size=512,
                 in_channels=3,
                 out_channels=3,
                 layers_per_block=4,
                 block_out_channels=(64, 128, 256, 512, 512, 768, 768, 1024),
                 down_block_types=(
                     "AttnDownBlock2D", "AttnDownBlock2D", "AttnDownBlock2D", 
                     "AttnDownBlock2D", "AttnDownBlock2D", "AttnDownBlock2D",
                     "DownBlock2D", "DownBlock2D"
                 ),
                 up_block_types=(
                     "UpBlock2D", "UpBlock2D", "AttnUpBlock2D", "AttnUpBlock2D",
                     "AttnUpBlock2D", "AttnUpBlock2D", "AttnUpBlock2D", "AttnUpBlock2D"
                 ),
                 attention_head_dim=32,
                 norm_num_groups=32):
        
        super().__init__()
        
        # Enhanced directional preprocessor
        self.directional_preprocessor = EnhancedFiberPreprocessor(in_channels)
        
        # Base UNet model
        self.base_unet = UNet2DModel(
            sample_size=sample_size,
            in_channels=in_channels,
            out_channels=out_channels,
            layers_per_block=layers_per_block,
            block_out_channels=block_out_channels,
            down_block_types=down_block_types,
            up_block_types=up_block_types,
            attention_head_dim=attention_head_dim,
            norm_num_groups=norm_num_groups,
        )
        
        # Initialize UNet weights properly
        self._init_unet_weights()
    
    def _init_unet_weights(self):
        """Initialize UNet weights using best practices for diffusion models"""
        def init_weights(m):
            if isinstance(m, nn.Conv2d):
                # Use Xavier uniform for conv layers
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                # Use Xavier normal for linear layers
                nn.init.xavier_normal_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, (nn.GroupNorm, nn.BatchNorm2d)):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
        
        self.base_unet.apply(init_weights)
    
    def forward(self, sample, timestep, return_dict=True):
        # Apply enhanced directional preprocessing
        enhanced_sample = self.directional_preprocessor(sample)
        
        # Forward through base UNet
        return self.base_unet(enhanced_sample, timestep, return_dict=return_dict)


# ═══════════════════════════════════════════════════════════════════
# DATASET CLASS FOR FIBER IMAGES
# ═══════════════════════════════════════════════════════════════════

class FiberDataset(Dataset):
    """Dataset class for fiber network images"""
    
    def __init__(self, image_dir, image_size=512, augment=True):
        self.image_paths = glob.glob(os.path.join(image_dir, "*.png")) + \
                          glob.glob(os.path.join(image_dir, "*.jpg")) + \
                          glob.glob(os.path.join(image_dir, "*.jpeg"))
        
        if len(self.image_paths) == 0:
            raise ValueError(f"No images found in {image_dir}")
        
        self.image_size = image_size
        
        # Base transforms
        self.base_transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])  # Normalize to [-1, 1]
        ])
        
        # Augmentation transforms
        if augment:
            self.augment_transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomVerticalFlip(p=0.5),
                transforms.RandomRotation(degrees=15),
                transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1),
                transforms.ToTensor(),
                transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
            ])
        else:
            self.augment_transform = self.base_transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        
        try:
            image = Image.open(image_path).convert('RGB')
            
            # Apply transforms
            if np.random.random() > 0.3:  # 70% chance for augmentation
                image = self.augment_transform(image)
            else:
                image = self.base_transform(image)
            
            return image
        
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            # Return a random tensor if image loading fails
            return torch.randn(3, self.image_size, self.image_size)


# ═══════════════════════════════════════════════════════════════════
# EMA IMPLEMENTATION COMPATIBLE WITH ACCELERATE
# ═══════════════════════════════════════════════════════════════════

class EMAModel:
    """
    Exponential Moving Average of models weights
    Compatible with Accelerate's distributed training
    """
    
    def __init__(self, model, decay=0.9999, device=None, update_after_step=0):
        self.decay = decay
        self.device = device
        self.update_after_step = update_after_step
        
        # Get the model parameters
        parameters = list(model.parameters())
        self.shadow_params = [p.clone().detach() for p in parameters]
        
        if device is not None:
            self.shadow_params = [p.to(device) for p in self.shadow_params]
        
        self.collected_params = []
        self.num_updates = 0

    def get_decay(self, optimization_step):
        """
        Compute the decay factor for the exponential moving average.
        """
        value = (1 + optimization_step) / (10 + optimization_step)
        return 1 - min(self.decay, value)

    @torch.no_grad()
    def step(self, model):
        """
        Step the EMA model.
        """
        parameters = list(model.parameters())

        if len(parameters) != len(self.shadow_params):
            raise ValueError(
                f"Number of parameters in model ({len(parameters)}) does not match "
                f"number of shadow parameters ({len(self.shadow_params)})"
            )

        decay = self.get_decay(self.num_updates)

        if self.num_updates >= self.update_after_step:
            for s_param, param in zip(self.shadow_params, parameters):
                if param.requires_grad:
                    s_param.sub_((s_param - param.data) * decay)

        self.num_updates += 1

    def copy_to(self, model):
        """
        Copy EMA parameters to the model.
        """
        parameters = list(model.parameters())
        if len(parameters) != len(self.shadow_params):
            raise ValueError(
                f"Number of parameters in model ({len(parameters)}) does not match "
                f"number of shadow parameters ({len(self.shadow_params)})"
            )

        for param, shadow_param in zip(parameters, self.shadow_params):
            if param.requires_grad:
                param.data.copy_(shadow_param.data)

    def to(self, device):
        """
        Move EMA model to device.
        """
        self.device = device
        self.shadow_params = [p.to(device) for p in self.shadow_params]
        return self

    def state_dict(self):
        """
        Return the state dict of the EMA model.
        """
        return {
            "shadow_params": self.shadow_params,
            "decay": self.decay,
            "num_updates": self.num_updates,
            "update_after_step": self.update_after_step,
        }

    def load_state_dict(self, state_dict):
        """
        Load the state dict of the EMA model.
        """
        self.shadow_params = state_dict["shadow_params"]
        self.decay = state_dict["decay"]
        self.num_updates = state_dict["num_updates"]
        self.update_after_step = state_dict["update_after_step"]


# ═══════════════════════════════════════════════════════════════════
# TRAINING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════

class FiberDiffusionConfig:
    """Configuration for fiber diffusion model training"""
    
    def __init__(self):
        # Model parameters
        self.image_size = 512
        self.in_channels = 3
        self.out_channels = 3
        self.layers_per_block = 4
        self.attention_head_dim = 32
        self.norm_num_groups = 32
        
        # Training parameters
        self.train_batch_size = 4  # Per device batch size
        self.eval_batch_size = 4
        self.learning_rate = 1e-4
        self.num_epochs = 500
        self.num_train_timesteps = 1000
        self.gradient_accumulation_steps = 4
        
        # Optimization
        self.weight_decay = 0.01
        self.beta1 = 0.9
        self.beta2 = 0.999
        self.eps = 1e-8
        
        # EMA
        self.ema_decay = 0.9999
        
        # Checkpointing and logging
        self.save_every = 10
        self.eval_every = 5
        self.output_dir = "outputs"
        
        # Data
        self.num_workers = 4
        
        # Accelerate-specific
        self.seed = 42


# ═══════════════════════════════════════════════════════════════════
# ACCELERATE-ENABLED TRAINER CLASS
# ═══════════════════════════════════════════════════════════════════

class AccelerateFiberDiffusionTrainer:
    """Accelerate-enabled trainer for fiber diffusion model"""
    
    def __init__(self, config, accelerator, train_dataloader, val_dataloader=None):
        self.config = config
        self.accelerator = accelerator
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        
        # Create model
        self.model = EnhancedFiberUNet(
            sample_size=config.image_size,
            in_channels=config.in_channels,
            out_channels=config.out_channels,
            layers_per_block=config.layers_per_block,
            attention_head_dim=config.attention_head_dim,
            norm_num_groups=config.norm_num_groups,
        )
        
        # Create noise scheduler
        self.noise_scheduler = DDPMScheduler(
            num_train_timesteps=config.num_train_timesteps,
            beta_schedule="scaled_linear",
            beta_start=0.0001,
            beta_end=0.02,
            clip_sample=False
        )
        
        # Create optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.learning_rate,
            betas=(config.beta1, config.beta2),
            weight_decay=config.weight_decay,
            eps=config.eps
        )
        
        # Create scheduler
        self.lr_scheduler = CosineAnnealingLR(
            self.optimizer, 
            T_max=config.num_epochs,
            eta_min=config.learning_rate * 0.01
        )
        
        # Prepare everything with Accelerate
        self.model, self.optimizer, self.train_dataloader, self.lr_scheduler = accelerator.prepare(
            self.model, self.optimizer, self.train_dataloader, self.lr_scheduler
        )
        
        if self.val_dataloader is not None:
            self.val_dataloader = accelerator.prepare(self.val_dataloader)
        
        # Create EMA model
        self.ema_model = EMAModel(
            accelerator.unwrap_model(self.model),
            decay=config.ema_decay,
            device=accelerator.device
        )
        
        # Register EMA for checkpointing (if supported)
        if hasattr(accelerator, 'register_for_checkpointing') and ACCELERATE_VERSION_OK:
            accelerator.register_for_checkpointing(self.ema_model)
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        
        # Setup logging
        if accelerator.is_main_process:
            logging.basicConfig(
                format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
                datefmt="%m/%d/%Y %H:%M:%S",
                level=logging.INFO,
            )
            self.logger = logging.getLogger(__name__)
            
            # Log model info
            num_params = sum(p.numel() for p in self.model.parameters())
            self.logger.info(f"Model initialized with {num_params:,} parameters")
        else:
            self.logger = None
    
    def train_step(self, batch):
        """Single training step"""
        clean_images = batch
        batch_size = clean_images.shape[0]
        
        # Sample random timesteps
        timesteps = torch.randint(
            0, self.noise_scheduler.config.num_train_timesteps,
            (batch_size,), device=clean_images.device
        ).long()
        
        # Add noise to images
        noise = torch.randn_like(clean_images)
        noisy_images = self.noise_scheduler.add_noise(clean_images, noise, timesteps)
        
        # Forward pass with automatic mixed precision
        with self.accelerator.autocast():
            # Predict noise
            predicted_noise = self.model(noisy_images, timesteps).sample
            # Compute loss
            loss = F.mse_loss(predicted_noise, noise)
        
        return loss
    
    def train_epoch(self):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        # Progress bar only on main process
        if self.accelerator.is_main_process:
            progress_bar = tqdm(
                range(len(self.train_dataloader)), 
                desc=f"Epoch {self.epoch}"
            )
        else:
            progress_bar = None
        
        for step, batch in enumerate(self.train_dataloader):
            # Use gradient accumulation context
            with self.accelerator.accumulate(self.model):
                # Training step
                loss = self.train_step(batch)
                
                # Backward pass
                self.accelerator.backward(loss)
                
                # Update parameters
                self.optimizer.step()
                self.optimizer.zero_grad()
                
                # Update EMA
                if self.accelerator.sync_gradients:
                    self.ema_model.step(self.accelerator.unwrap_model(self.model))
                    self.global_step += 1
            
            # Update metrics
            total_loss += loss.item()
            num_batches += 1
            
            # Update progress bar on main process
            if progress_bar is not None:
                progress_bar.update(1)
                progress_bar.set_postfix({
                    'loss': f'{loss.item():.4f}',
                    'lr': f'{self.optimizer.param_groups[0]["lr"]:.2e}',
                    'step': self.global_step
                })
        
        if progress_bar is not None:
            progress_bar.close()
        
        return total_loss / num_batches
    
    @torch.no_grad()
    def validate(self):
        """Validation step"""
        if self.val_dataloader is None:
            return 0.0
        
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        for batch in self.val_dataloader:
            clean_images = batch
            batch_size = clean_images.shape[0]
            
            # Sample random timesteps
            timesteps = torch.randint(
                0, self.noise_scheduler.config.num_train_timesteps,
                (batch_size,), device=clean_images.device
            ).long()
            
            # Add noise to images
            noise = torch.randn_like(clean_images)
            noisy_images = self.noise_scheduler.add_noise(clean_images, noise, timesteps)
            
            # Predict noise
            predicted_noise = self.model(noisy_images, timesteps).sample
            loss = F.mse_loss(predicted_noise, noise)
            
            # Gather loss from all processes
            loss = self.accelerator.gather(loss)
            total_loss += loss.mean().item()
            num_batches += 1
        
        return total_loss / num_batches
    
    @torch.no_grad()
    def sample_images(self, num_samples=4):
        """Generate sample images using EMA model"""
        # Use EMA model for inference
        ema_model = self.accelerator.unwrap_model(self.model)
        self.ema_model.copy_to(ema_model)
        ema_model.eval()
        
        # Start with random noise
        images = torch.randn(
            num_samples, self.config.in_channels, 
            self.config.image_size, self.config.image_size,
            device=self.accelerator.device
        )
        
        # Set scheduler timesteps
        self.noise_scheduler.set_timesteps(50)  # Use fewer steps for faster sampling
        
        # Denoising loop
        if self.accelerator.is_main_process:
            progress_bar = tqdm(self.noise_scheduler.timesteps, desc="Sampling")
        else:
            progress_bar = self.noise_scheduler.timesteps
        
        for t in progress_bar:
            # Predict noise
            with self.accelerator.autocast():
                noise_pred = ema_model(images, t).sample
            
            # Remove noise
            images = self.noise_scheduler.step(noise_pred, t, images).prev_sample
        
        # Denormalize images to [0, 1]
        images = (images + 1) / 2
        images = torch.clamp(images, 0, 1)
        
        return images
    
    def train(self):
        """Main training loop"""
        self.accelerator.print("Starting training...")
        
        # Resume from checkpoint if available
        checkpoint_latest = os.path.join(self.config.output_dir, "checkpoint_latest")
        if os.path.exists(checkpoint_latest):
            self.accelerator.print("Resuming from latest checkpoint...")
            if hasattr(self.accelerator, 'load_state') and ACCELERATE_VERSION_OK:
                self.accelerator.load_state(checkpoint_latest)
            else:
                self._load_checkpoint_manual(checkpoint_latest)
        
        for epoch in range(self.epoch, self.config.num_epochs):
            self.epoch = epoch
            
            # Train epoch
            train_loss = self.train_epoch()
            
            # Update learning rate
            self.lr_scheduler.step()
            
            # Wait for all processes
            self.accelerator.wait_for_everyone()
            
            # Validation and sampling (only on main process)
            if epoch % self.config.eval_every == 0:
                val_loss = self.validate()
                
                if self.accelerator.is_main_process:
                    self.logger.info(f"Epoch {epoch}: Train Loss = {train_loss:.4f}, Val Loss = {val_loss:.4f}")
                    
                    # Generate sample images
                    sample_images = self.sample_images()
                    
                    # Save sample images
                    os.makedirs(self.config.output_dir, exist_ok=True)
                    import torchvision.utils as vutils
                    vutils.save_image(
                        sample_images, 
                        os.path.join(self.config.output_dir, f"samples_epoch_{epoch}.png"),
                        nrow=2, normalize=False
                    )
            
            # Save checkpoint
            if epoch % self.config.save_every == 0:
                self.accelerator.wait_for_everyone()
                
                if self.accelerator.is_main_process:
                    # Create checkpoint directory
                    checkpoint_path = os.path.join(self.config.output_dir, f"checkpoint_epoch_{epoch}")
                    os.makedirs(checkpoint_path, exist_ok=True)
                    
                    # Save state with Accelerate (if supported)
                    if hasattr(self.accelerator, 'save_state') and ACCELERATE_VERSION_OK:
                        self.accelerator.save_state(checkpoint_path)
                    else:
                        # Fallback for older accelerate versions
                        self._save_checkpoint_manual(checkpoint_path)
                    
                    # Also save as latest
                    latest_path = os.path.join(self.config.output_dir, "checkpoint_latest")
                    os.makedirs(latest_path, exist_ok=True)
                    if hasattr(self.accelerator, 'save_state') and ACCELERATE_VERSION_OK:
                        self.accelerator.save_state(latest_path)
                    else:
                        self._save_checkpoint_manual(latest_path)
                    
                    self.logger.info(f"Checkpoint saved to {checkpoint_path}")
        
        self.accelerator.print("Training completed!")
        
        # Final model save
        self.accelerator.wait_for_everyone()
        if self.accelerator.is_main_process:
            # Save the final model
            unwrapped_model = self.accelerator.unwrap_model(self.model)
            self.ema_model.copy_to(unwrapped_model)
            
            final_model_path = os.path.join(self.config.output_dir, "final_model")
            os.makedirs(final_model_path, exist_ok=True)
            
            # Save model weights
            torch.save(unwrapped_model.state_dict(), os.path.join(final_model_path, "pytorch_model.bin"))
            
            # Save config
            import json
            config_dict = {
                "sample_size": self.config.image_size,
                "in_channels": self.config.in_channels,
                "out_channels": self.config.out_channels,
                "layers_per_block": self.config.layers_per_block,
                "attention_head_dim": self.config.attention_head_dim,
                "norm_num_groups": self.config.norm_num_groups,
            }
            
            with open(os.path.join(final_model_path, "config.json"), "w") as f:
                json.dump(config_dict, f, indent=2)
            
            self.logger.info(f"Final model saved to {final_model_path}")
    
    def _save_checkpoint_manual(self, checkpoint_path):
        """Manual checkpoint saving for older accelerate versions"""
        # Save model
        unwrapped_model = self.accelerator.unwrap_model(self.model)
        torch.save(unwrapped_model.state_dict(), os.path.join(checkpoint_path, "pytorch_model.bin"))
        
        # Save optimizer
        torch.save(self.optimizer.state_dict(), os.path.join(checkpoint_path, "optimizer.bin"))
        
        # Save scheduler
        torch.save(self.lr_scheduler.state_dict(), os.path.join(checkpoint_path, "scheduler.bin"))
        
        # Save EMA
        torch.save(self.ema_model.state_dict(), os.path.join(checkpoint_path, "ema_model.bin"))
        
        # Save training state
        training_state = {
            'epoch': self.epoch,
            'global_step': self.global_step,
        }
        torch.save(training_state, os.path.join(checkpoint_path, "training_state.bin"))
    
    def _load_checkpoint_manual(self, checkpoint_path):
        """Manual checkpoint loading for older accelerate versions"""
        # Load model
        model_path = os.path.join(checkpoint_path, "pytorch_model.bin")
        if os.path.exists(model_path):
            unwrapped_model = self.accelerator.unwrap_model(self.model)
            unwrapped_model.load_state_dict(torch.load(model_path, map_location=self.accelerator.device))
        
        # Load optimizer
        optimizer_path = os.path.join(checkpoint_path, "optimizer.bin")
        if os.path.exists(optimizer_path):
            self.optimizer.load_state_dict(torch.load(optimizer_path, map_location=self.accelerator.device))
        
        # Load scheduler
        scheduler_path = os.path.join(checkpoint_path, "scheduler.bin")
        if os.path.exists(scheduler_path):
            self.lr_scheduler.load_state_dict(torch.load(scheduler_path, map_location=self.accelerator.device))
        
        # Load EMA
        ema_path = os.path.join(checkpoint_path, "ema_model.bin")
        if os.path.exists(ema_path):
            self.ema_model.load_state_dict(torch.load(ema_path, map_location=self.accelerator.device))
        
        # Load training state
        state_path = os.path.join(checkpoint_path, "training_state.bin")
        if os.path.exists(state_path):
            training_state = torch.load(state_path, map_location=self.accelerator.device)
            self.epoch = training_state['epoch']
            self.global_step = training_state['global_step']


# ═══════════════════════════════════════════════════════════════════
# MAIN TRAINING SCRIPT
# ═══════════════════════════════════════════════════════════════════

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Train Enhanced Fiber Diffusion Model with Accelerate")
    
    parser.add_argument(
        "--train_data_dir",
        type=str,
        required=True,
        help="Path to training data directory",
    )
    parser.add_argument(
        "--val_data_dir", 
        type=str,
        default=None,
        help="Path to validation data directory",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="./fiber_diffusion_outputs",
        help="Output directory for checkpoints and samples",
    )
    parser.add_argument(
        "--image_size",
        type=int,
        default=512,
        help="Image resolution",
    )
    parser.add_argument(
        "--train_batch_size",
        type=int,
        default=4,
        help="Training batch size per device",
    )
    parser.add_argument(
        "--eval_batch_size",
        type=int,
        default=4,
        help="Evaluation batch size per device",
    )
    parser.add_argument(
        "--num_epochs",
        type=int,
        default=100,
        help="Number of training epochs",
    )
    parser.add_argument(
        "--learning_rate",
        type=float,
        default=1e-4,
        help="Learning rate",
    )
    parser.add_argument(
        "--gradient_accumulation_steps",
        type=int,
        default=4,
        help="Number of gradient accumulation steps",
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=42,
        help="Random seed",
    )
    
    return parser.parse_args()


def main():
    """Main training function"""
    args = parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Create project configuration for automatic checkpoint naming (if available)
    project_config = None
    if HAS_PROJECT_CONFIG and ACCELERATE_VERSION_OK:
        project_config = ProjectConfiguration(
            project_dir=args.output_dir,
            automatic_checkpoint_naming=True,
            total_limit=5  # Keep only 5 latest checkpoints
        )
    
    # Initialize Accelerator (simplified - no logging for now to avoid errors)
    accelerator_kwargs = {
        'gradient_accumulation_steps': args.gradient_accumulation_steps,
    }
    
    # Add project config if available
    if project_config is not None:
        accelerator_kwargs['project_config'] = project_config
        
    accelerator = Accelerator(**accelerator_kwargs)
    
    # Set seed for reproducible training
    set_seed(args.seed)
    
    # Create configuration
    config = FiberDiffusionConfig()
    config.train_batch_size = args.train_batch_size
    config.eval_batch_size = args.eval_batch_size
    config.num_epochs = args.num_epochs
    config.learning_rate = args.learning_rate
    config.gradient_accumulation_steps = args.gradient_accumulation_steps
    config.image_size = args.image_size
    config.output_dir = args.output_dir
    config.seed = args.seed
    
    # Log configuration on main process
    accelerator.print(f"Training configuration:")
    accelerator.print(f"  Image size: {config.image_size}")
    accelerator.print(f"  Batch size per device: {config.train_batch_size}")
    accelerator.print(f"  Number of epochs: {config.num_epochs}")
    accelerator.print(f"  Learning rate: {config.learning_rate}")
    accelerator.print(f"  Gradient accumulation steps: {config.gradient_accumulation_steps}")
    accelerator.print(f"  Output directory: {config.output_dir}")
    accelerator.print(f"  Device: {accelerator.device}")
    accelerator.print(f"  Number of processes: {accelerator.num_processes}")
    accelerator.print(f"  Mixed precision: {accelerator.mixed_precision}")
    
    # Create datasets
    accelerator.print(f"Loading training data from: {args.train_data_dir}")
    train_dataset = FiberDataset(
        image_dir=args.train_data_dir,
        image_size=config.image_size,
        augment=True
    )
    
    val_dataset = None
    if args.val_data_dir:
        accelerator.print(f"Loading validation data from: {args.val_data_dir}")
        val_dataset = FiberDataset(
            image_dir=args.val_data_dir,
            image_size=config.image_size,
            augment=False
        )
    
    accelerator.print(f"Training dataset size: {len(train_dataset)}")
    if val_dataset:
        accelerator.print(f"Validation dataset size: {len(val_dataset)}")
    
    # Create dataloaders
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=config.train_batch_size,
        shuffle=True,
        num_workers=config.num_workers,
        pin_memory=True
    )
    
    val_dataloader = None
    if val_dataset:
        val_dataloader = DataLoader(
            val_dataset,
            batch_size=config.eval_batch_size,
            shuffle=False,
            num_workers=config.num_workers,
            pin_memory=True
        )
    
    # Create trainer
    trainer = AccelerateFiberDiffusionTrainer(
        config=config,
        accelerator=accelerator,
        train_dataloader=train_dataloader,
        val_dataloader=val_dataloader
    )
    
    # Start training
    trainer.train()


def test_model():
    """Test the model with dummy data"""
    print("Testing Enhanced Fiber Diffusion Model with Accelerate...")
    
    # Check accelerate version
    import accelerate
    print(f"Accelerate version: {accelerate.__version__}")
    
    if HAS_PROJECT_CONFIG and ACCELERATE_VERSION_OK:
        print("✅ Using latest Accelerate version with full features!")
    elif not HAS_PROJECT_CONFIG:
        print("⚠️  ProjectConfiguration not available in this Accelerate version")
        print("   Using manual checkpoint saving instead")
    
    # Create minimal accelerator for testing
    accelerator = Accelerator(cpu=True)
    
    # Create model
    print("Creating Enhanced Fiber UNet model...")
    model = EnhancedFiberUNet()
    
    # Prepare model with accelerator
    print("Preparing model with Accelerate...")
    model = accelerator.prepare(model)
    
    # Test with dummy input
    batch_size = 2
    dummy_input = torch.randn(batch_size, 3, 512, 512)
    dummy_timesteps = torch.randint(0, 1000, (batch_size,))
    
    print("Running forward pass...")
    # Forward pass
    with torch.no_grad():
        output = model(dummy_input, dummy_timesteps)
    
    accelerator.print("=" * 50)
    accelerator.print("✅ MODEL TEST RESULTS")
    accelerator.print("=" * 50)
    accelerator.print(f"✅ Input shape: {dummy_input.shape}")
    accelerator.print(f"✅ Output shape: {output.sample.shape}")
    accelerator.print(f"✅ Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    accelerator.print(f"✅ Directional filters: 8 types (linear + curved)")
    accelerator.print(f"✅ Enhanced fiber detection: ENABLED")
    accelerator.print("✅ Model test successful!")
    accelerator.print("=" * 50)


if __name__ == "__main__":
    # For testing the model
    if len(os.sys.argv) == 1:
        test_model()
    else:
        # For actual training
        main()
