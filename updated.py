import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from diffusers import UNet2DModel, DDPMScheduler
from accelerate import Accelerator
from accelerate.utils import set_seed
import numpy as np
import math
import os
import glob
from PIL import Image
from torchvision import transforms
from tqdm import tqdm
import logging
from torch.optim.lr_scheduler import CosineAnnealingLR
import argparse
from typing import Optional, Tuple, List
import json
from pathlib import Path

# Try to import ProjectConfiguration for newer versions
try:
    from accelerate import ProjectConfiguration
    HAS_PROJECT_CONFIG = True
except ImportError:
    HAS_PROJECT_CONFIG = False
    ProjectConfiguration = None


# ═══════════════════════════════════════════════════════════════════
# ENHANCED DIRECTIONAL COMPONENTS WITH LEARNABLE FILTERS
# ═══════════════════════════════════════════════════════════════════

class LearnableDirectionalFilter(nn.Module):
    """Learnable directional filter with initialization bias"""
    
    def __init__(self, in_channels, out_channels, kernel_size, filter_type='gabor'):
        super().__init__()
        self.kernel_size = kernel_size
        self.filter_type = filter_type
        
        # Create the convolutional layer
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, padding=kernel_size//2)
        
        # Initialize with directional bias but keep it learnable
        self._initialize_filter()
    
    def _initialize_filter(self):
        """Initialize filters with directional patterns but keep them learnable"""
        with torch.no_grad():
            if self.filter_type == 'gabor':
                # Initialize with Gabor-like patterns for better edge detection
                for i in range(self.conv.out_channels):
                    theta = (i * np.pi) / self.conv.out_channels
                    kernel = self._create_gabor_kernel(self.kernel_size, theta)
                    for j in range(self.conv.in_channels):
                        self.conv.weight.data[i, j] = kernel
            else:
                # Use Xavier initialization with slight directional bias
                nn.init.xavier_uniform_(self.conv.weight)
                # Add small directional bias without destroying gradients
                bias = self._create_directional_bias(self.kernel_size, self.filter_type)
                self.conv.weight.data += 0.1 * bias.unsqueeze(0).unsqueeze(0)
    
    def _create_gabor_kernel(self, size, theta, sigma=None, Lambda=None, gamma=0.5):
        """Create a Gabor kernel for edge detection"""
        if sigma is None:
            sigma = size / 4.0
        if Lambda is None:
            Lambda = size / 2.0
            
        kernel = torch.zeros(size, size)
        center = size // 2
        
        for i in range(size):
            for j in range(size):
                x = i - center
                y = j - center
                
                # Rotate coordinates
                x_theta = x * np.cos(theta) + y * np.sin(theta)
                y_theta = -x * np.sin(theta) + y * np.cos(theta)
                
                # Gabor function
                exp_part = torch.exp(-(x_theta**2 + gamma**2 * y_theta**2) / (2 * sigma**2))
                cos_part = torch.cos(2 * np.pi * x_theta / Lambda)
                kernel[i, j] = exp_part * cos_part
        
        return kernel / kernel.abs().max()
    
    def _create_directional_bias(self, size, filter_type):
        """Create directional bias patterns"""
        kernel = torch.zeros(size, size)
        center = size // 2
        
        if filter_type == 'horizontal':
            kernel[center, :] = 1.0
        elif filter_type == 'vertical':
            kernel[:, center] = 1.0
        elif filter_type == 'diagonal_45':
            for i in range(size):
                if 0 <= i < size:
                    kernel[i, i] = 1.0
        elif filter_type == 'diagonal_135':
            for i in range(size):
                if 0 <= size-1-i < size:
                    kernel[i, size-1-i] = 1.0
        elif filter_type == 'circular':
            radius = size // 3
            for i in range(size):
                for j in range(size):
                    dist = math.sqrt((i - center)**2 + (j - center)**2)
                    if abs(dist - radius) <= 1.0:
                        kernel[i, j] = 1.0
        
        return kernel / (kernel.sum() + 1e-8)
    
    def forward(self, x):
        return self.conv(x)


class ImprovedDirectionalBlock(nn.Module):
    """Improved directional block with better channel management and learnable filters"""
    
    def __init__(self, in_channels, expansion_factor=4):
        super().__init__()
        
        # Calculate output channels to avoid GroupNorm issues
        base_channels = max(8, in_channels // 2)
        # Ensure divisible by 8 for GroupNorm
        base_channels = (base_channels // 8) * 8
        if base_channels == 0:
            base_channels = 8
            
        self.out_channels_per_filter = base_channels
        self.num_filters = 8
        self.total_out_channels = self.out_channels_per_filter * self.num_filters
        
        # Create learnable directional filters
        self.filters = nn.ModuleList([
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 7, 'horizontal'),
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 7, 'vertical'),
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 5, 'diagonal_45'),
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 5, 'diagonal_135'),
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 7, 'gabor'),
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 9, 'gabor'),
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 11, 'circular'),
            LearnableDirectionalFilter(in_channels, self.out_channels_per_filter, 7, 'gabor'),
        ])
        
        # Improved normalization - ensure num_groups divides channels
        num_groups = min(8, self.total_out_channels)
        while self.total_out_channels % num_groups != 0:
            num_groups -= 1
        self.norm = nn.GroupNorm(num_groups, self.total_out_channels)
        self.activation = nn.SiLU()
        
        # Multi-scale feature aggregation to preserve information
        self.multi_scale_conv = nn.Conv2d(self.total_out_channels, self.total_out_channels, 1)
        
    def forward(self, x):
        # Apply all directional filters
        features = []
        for filter_module in self.filters:
            features.append(filter_module(x))
        
        # Concatenate all features
        combined = torch.cat(features, dim=1)
        
        # Apply normalization and activation
        normalized = self.activation(self.norm(combined))
        
        # Multi-scale aggregation
        aggregated = self.multi_scale_conv(normalized)
        
        return aggregated + normalized  # Residual connection


class EnhancedFiberPreprocessor(nn.Module):
    """Enhanced preprocessor with better information preservation"""
    
    def __init__(self, in_channels=3, preserve_factor=0.5):
        super().__init__()
        
        self.directional_block = ImprovedDirectionalBlock(in_channels)
        directional_channels = self.directional_block.total_out_channels
        
        # Multi-path fusion to preserve more information
        self.path1 = nn.Sequential(
            nn.Conv2d(directional_channels, in_channels * 2, 3, padding=1),
            nn.GroupNorm(min(8, in_channels * 2), in_channels * 2),
            nn.SiLU(),
        )
        
        self.path2 = nn.Sequential(
            nn.Conv2d(directional_channels, in_channels * 2, 5, padding=2),
            nn.GroupNorm(min(8, in_channels * 2), in_channels * 2),
            nn.SiLU(),
        )
        
        # Final fusion with residual
        self.fusion = nn.Conv2d(in_channels * 4 + in_channels, in_channels, 1)
        self.preserve_factor = preserve_factor
        
    def forward(self, x):
        # Extract directional features
        directional = self.directional_block(x)
        
        # Multi-path processing
        path1_out = self.path1(directional)
        path2_out = self.path2(directional)
        
        # Concatenate all paths with original
        combined = torch.cat([x, path1_out, path2_out], dim=1)
        
        # Fusion with preservation
        fused = self.fusion(combined)
        
        # Preserve original information
        return self.preserve_factor * x + (1 - self.preserve_factor) * fused


# ═══════════════════════════════════════════════════════════════════
# ENHANCED UNET WITH STRUCTURAL LOSS
# ═══════════════════════════════════════════════════════════════════

class StructuralLoss(nn.Module):
    """Combined loss for better fiber structure preservation"""
    
    def __init__(self, mse_weight=0.7, edge_weight=0.3):
        super().__init__()
        self.mse_weight = mse_weight
        self.edge_weight = edge_weight
        
        # Sobel filters for edge detection
        self.register_buffer('sobel_x', torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32).view(1, 1, 3, 3))
        self.register_buffer('sobel_y', torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32).view(1, 1, 3, 3))
    
    def edge_loss(self, pred, target):
        """Compute edge-aware loss"""
        # Convert to grayscale if needed
        if pred.shape[1] == 3:
            pred_gray = 0.299 * pred[:, 0:1] + 0.587 * pred[:, 1:2] + 0.114 * pred[:, 2:3]
            target_gray = 0.299 * target[:, 0:1] + 0.587 * target[:, 1:2] + 0.114 * target[:, 2:3]
        else:
            pred_gray = pred
            target_gray = target
        
        # Compute edges
        pred_edges_x = F.conv2d(pred_gray, self.sobel_x, padding=1)
        pred_edges_y = F.conv2d(pred_gray, self.sobel_y, padding=1)
        pred_edges = torch.sqrt(pred_edges_x**2 + pred_edges_y**2 + 1e-8)
        
        target_edges_x = F.conv2d(target_gray, self.sobel_x, padding=1)
        target_edges_y = F.conv2d(target_gray, self.sobel_y, padding=1)
        target_edges = torch.sqrt(target_edges_x**2 + target_edges_y**2 + 1e-8)
        
        return F.l1_loss(pred_edges, target_edges)
    
    def forward(self, pred, target):
        """Combined structural loss"""
        mse_loss = F.mse_loss(pred, target)
        edge_loss = self.edge_loss(pred, target)
        
        return self.mse_weight * mse_loss + self.edge_weight * edge_loss


class EnhancedFiberUNet(nn.Module):
    """UNet with enhanced preprocessing and structural awareness"""
    
    def __init__(self, 
                 sample_size=512,
                 in_channels=3,
                 out_channels=3,
                 layers_per_block=4,
                 block_out_channels=(64, 128, 256, 512, 512, 768, 768, 1024),
                 down_block_types=(
                     "AttnDownBlock2D", "AttnDownBlock2D", "AttnDownBlock2D", 
                     "AttnDownBlock2D", "AttnDownBlock2D", "AttnDownBlock2D",
                     "DownBlock2D", "DownBlock2D"
                 ),
                 up_block_types=(
                     "UpBlock2D", "UpBlock2D", "AttnUpBlock2D", "AttnUpBlock2D",
                     "AttnUpBlock2D", "AttnUpBlock2D", "AttnUpBlock2D", "AttnUpBlock2D"
                 ),
                 attention_head_dim=32,
                 norm_num_groups=32,
                 use_structural_loss=True):
        
        super().__init__()
        
        # Enhanced directional preprocessor
        self.directional_preprocessor = EnhancedFiberPreprocessor(in_channels)
        
        # Base UNet model
        self.base_unet = UNet2DModel(
            sample_size=sample_size,
            in_channels=in_channels,
            out_channels=out_channels,
            layers_per_block=layers_per_block,
            block_out_channels=block_out_channels,
            down_block_types=down_block_types,
            up_block_types=up_block_types,
            attention_head_dim=attention_head_dim,
            norm_num_groups=norm_num_groups,
        )
        
        # Initialize weights properly
        self.apply(self._init_weights)
        
        # Loss function
        if use_structural_loss:
            self.loss_fn = StructuralLoss()
        else:
            self.loss_fn = nn.MSELoss()
    
    def _init_weights(self, m):
        if isinstance(m, (nn.Conv2d, nn.Linear)):
            nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.zeros_(m.bias)
        elif isinstance(m, nn.GroupNorm):
            nn.init.ones_(m.weight)
            nn.init.zeros_(m.bias)
    
    def forward(self, sample, timestep, return_dict=True):
        # Apply enhanced directional preprocessing
        enhanced_sample = self.directional_preprocessor(sample)
        
        # Forward through base UNet
        return self.base_unet(enhanced_sample, timestep, return_dict=return_dict)


# ═══════════════════════════════════════════════════════════════════
# CORRECTED EMA IMPLEMENTATION
# ═══════════════════════════════════════════════════════════════════

class EMAModel:
    """
    Corrected Exponential Moving Average implementation
    Following best practices from diffusion model research
    """
    
    def __init__(self, model, decay=0.9999, min_decay=0.0, update_after_step=0, update_every=1):
        """
        Args:
            model: The model to track
            decay: Maximum decay rate
            min_decay: Minimum decay rate for warmup
            update_after_step: Start updating after this many steps
            update_every: Update every N steps to save compute
        """
        self.decay = decay
        self.min_decay = min_decay
        self.update_after_step = update_after_step
        self.update_every = update_every
        
        # Create shadow copy of model parameters
        self.shadow_params = [p.clone().detach() for p in model.parameters()]
        
        # Track optimization steps
        self.optimization_step = 0
        self.num_updates = 0

    def get_decay(self):
        """
        Compute decay rate with proper warmup
        Corrected formula: starts at min_decay and increases to decay
        """
        step = max(0, self.num_updates - self.update_after_step)
        
        if step <= 0:
            return self.min_decay
        
        # Correct warmup: decay increases over time
        value = 1 - (1 + step) / (10 + step)
        value = self.min_decay + (self.decay - self.min_decay) * value
        return min(self.decay, max(self.min_decay, value))

    @torch.no_grad()
    def update(self, model):
        """Update shadow parameters"""
        self.optimization_step += 1
        
        # Only update every N steps
        if self.optimization_step % self.update_every != 0:
            return
        
        if self.optimization_step < self.update_after_step:
            return
            
        self.num_updates += 1
        decay = self.get_decay()
        
        # Update shadow parameters
        model_params = list(model.parameters())
        for s_param, m_param in zip(self.shadow_params, model_params):
            if m_param.requires_grad:
                # Correct EMA formula
                s_param.mul_(decay).add_(m_param.data, alpha=1 - decay)

    def copy_to(self, model):
        """Copy shadow parameters to model"""
        model_params = list(model.parameters())
        for s_param, m_param in zip(self.shadow_params, model_params):
            if m_param.requires_grad:
                m_param.data.copy_(s_param.data)

    def to(self, device):
        """Move shadow parameters to device"""
        self.shadow_params = [p.to(device) for p in self.shadow_params]
        return self

    def state_dict(self):
        return {
            "decay": self.decay,
            "min_decay": self.min_decay,
            "optimization_step": self.optimization_step,
            "num_updates": self.num_updates,
            "update_after_step": self.update_after_step,
            "update_every": self.update_every,
            "shadow_params": self.shadow_params,
        }

    def load_state_dict(self, state_dict):
        self.decay = state_dict["decay"]
        self.min_decay = state_dict.get("min_decay", 0.0)
        self.optimization_step = state_dict["optimization_step"]
        self.num_updates = state_dict["num_updates"]
        self.update_after_step = state_dict["update_after_step"]
        self.update_every = state_dict.get("update_every", 1)
        self.shadow_params = state_dict["shadow_params"]


# ═══════════════════════════════════════════════════════════════════
# IMPROVED DATASET WITH BETTER AUGMENTATION
# ═══════════════════════════════════════════════════════════════════

class FiberDataset(Dataset):
    """Dataset class for fiber network images with improved augmentation"""
    
    def __init__(self, image_dir, image_size=512, augment=True):
        self.image_paths = []
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.PNG', '*.JPG', '*.JPEG']:
            self.image_paths.extend(glob.glob(os.path.join(image_dir, ext)))
        
        if len(self.image_paths) == 0:
            raise ValueError(f"No images found in {image_dir}")
        
        self.image_size = image_size
        
        # Base transforms
        self.base_transform = transforms.Compose([
            transforms.Resize((image_size, image_size), antialias=True),
            transforms.ToTensor(),
            transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
        ])
        
        # Augmentation transforms for fiber structures
        if augment:
            self.augment_transform = transforms.Compose([
                transforms.Resize((image_size, image_size), antialias=True),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomVerticalFlip(p=0.5),
                transforms.RandomRotation(degrees=30),  # Increased rotation
                transforms.RandomAffine(degrees=0, translate=(0.1, 0.1)),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.1),
                transforms.RandomGrayscale(p=0.1),  # Sometimes fibers are grayscale
                transforms.ToTensor(),
                transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
            ])
        else:
            self.augment_transform = self.base_transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        
        try:
            image = Image.open(image_path).convert('RGB')
            
            # Apply transforms
            if hasattr(self, 'augment_transform') and np.random.random() > 0.2:
                image = self.augment_transform(image)
            else:
                image = self.base_transform(image)
            
            return image
        
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            # Return a valid tensor on error
            return torch.randn(3, self.image_size, self.image_size) * 0.1


# ═══════════════════════════════════════════════════════════════════
# TRAINING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════

class FiberDiffusionConfig:
    """Configuration for fiber diffusion model training"""
    
    def __init__(self):
        # Model parameters
        self.image_size = 512
        self.in_channels = 3
        self.out_channels = 3
        self.layers_per_block = 4
        self.attention_head_dim = 32
        self.norm_num_groups = 32
        
        # Training parameters
        self.train_batch_size = 4
        self.eval_batch_size = 4
        self.learning_rate = 1e-4
        self.num_epochs = 500
        self.num_train_timesteps = 1000
        self.gradient_accumulation_steps = 4
        
        # Optimization
        self.weight_decay = 0.01
        self.beta1 = 0.9
        self.beta2 = 0.999
        self.eps = 1e-8
        
        # EMA - Corrected parameters
        self.ema_decay = 0.9999
        self.ema_min_decay = 0.0
        self.ema_update_after_step = 1000
        self.ema_update_every = 10
        
        # Sampling
        self.num_inference_steps = 50  # Can be different from training
        
        # Loss
        self.use_structural_loss = True
        
        # Checkpointing and logging
        self.save_every = 10
        self.eval_every = 5
        self.output_dir = "outputs"
        
        # Data
        self.num_workers = 4
        
        # Accelerate-specific
        self.seed = 42
        self.mixed_precision = "fp16"  # or "bf16" or "no"


# ═══════════════════════════════════════════════════════════════════
# IMPROVED TRAINER WITH FIXES
# ═══════════════════════════════════════════════════════════════════

class AccelerateFiberDiffusionTrainer:
    """Improved trainer with all fixes applied"""
    
    def __init__(self, config, accelerator, train_dataloader, val_dataloader=None):
        self.config = config
        self.accelerator = accelerator
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        
        # Create model with structural loss
        self.model = EnhancedFiberUNet(
            sample_size=config.image_size,
            in_channels=config.in_channels,
            out_channels=config.out_channels,
            layers_per_block=config.layers_per_block,
            attention_head_dim=config.attention_head_dim,
            norm_num_groups=config.norm_num_groups,
            use_structural_loss=config.use_structural_loss,
        )
        
        # Create noise scheduler
        self.noise_scheduler = DDPMScheduler(
            num_train_timesteps=config.num_train_timesteps,
            beta_schedule="scaled_linear",
            beta_start=0.0001,
            beta_end=0.02,
            clip_sample=False,
            prediction_type="epsilon",
        )
        
        # Create optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.learning_rate,
            betas=(config.beta1, config.beta2),
            weight_decay=config.weight_decay,
            eps=config.eps
        )
        
        # Create scheduler
        num_training_steps = len(train_dataloader) * config.num_epochs
        self.lr_scheduler = CosineAnnealingLR(
            self.optimizer, 
            T_max=num_training_steps,
            eta_min=config.learning_rate * 0.01
        )
        
        # Prepare everything with Accelerate
        self.model, self.optimizer, self.train_dataloader, self.lr_scheduler = accelerator.prepare(
            self.model, self.optimizer, self.train_dataloader, self.lr_scheduler
        )
        
        if self.val_dataloader is not None:
            self.val_dataloader = accelerator.prepare(self.val_dataloader)
        
        # Create corrected EMA model
        self.ema_model = EMAModel(
            accelerator.unwrap_model(self.model),
            decay=config.ema_decay,
            min_decay=config.ema_min_decay,
            update_after_step=config.ema_update_after_step,
            update_every=config.ema_update_every
        )
        self.ema_model.to(accelerator.device)
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        
        # Setup logging
        if accelerator.is_main_process:
            logging.basicConfig(
                format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
                datefmt="%m/%d/%Y %H:%M:%S",
                level=logging.INFO,
            )
            self.logger = logging.getLogger(__name__)
            
            # Log configuration
            self._log_configuration()
    
    def _log_configuration(self):
        """Log training configuration"""
        if self.logger:
            num_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            self.logger.info(f"Model initialized with {num_params:,} trainable parameters")
            self.logger.info(f"Using {'structural' if self.config.use_structural_loss else 'MSE'} loss")
            self.logger.info(f"EMA decay: {self.config.ema_decay} (min: {self.config.ema_min_decay})")
            self.logger.info(f"Mixed precision: {self.config.mixed_precision}")
    
    def train_step(self, batch):
        """Single training step with structural loss"""
        clean_images = batch
        batch_size = clean_images.shape[0]
        
        # Sample random timesteps
        timesteps = torch.randint(
            0, self.noise_scheduler.config.num_train_timesteps,
            (batch_size,), device=clean_images.device
        ).long()
        
        # Add noise to images
        noise = torch.randn_like(clean_images)
        noisy_images = self.noise_scheduler.add_noise(clean_images, noise, timesteps)
        
        # Forward pass
        with self.accelerator.autocast():
            # Predict noise
            model_output = self.model(noisy_images, timesteps)
            predicted_noise = model_output.sample
            
            # Compute loss using the model's loss function
            if hasattr(self.model.module, 'loss_fn'):
                loss = self.model.module.loss_fn(predicted_noise, noise)
            else:
                loss = F.mse_loss(predicted_noise, noise)
        
        return loss
    
    def train_epoch(self):
        """Train for one epoch with proper synchronization"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        # Progress bar only on main process
        progress_bar = None
        if self.accelerator.is_main_process:
            progress_bar = tqdm(
                total=len(self.train_dataloader),
                desc=f"Epoch {self.epoch}",
                leave=True
            )
        
        for step, batch in enumerate(self.train_dataloader):
            with self.accelerator.accumulate(self.model):
                # Training step
                loss = self.train_step(batch)
                
                # Backward pass
                self.accelerator.backward(loss)
                
                # Gradient clipping
                if self.accelerator.sync_gradients:
                    self.accelerator.clip_grad_norm_(self.model.parameters(), 1.0)
                
                # Update parameters
                self.optimizer.step()
                self.lr_scheduler.step()
                self.optimizer.zero_grad()
                
                # Update EMA with synchronization
                if self.accelerator.sync_gradients:
                    # Ensure all processes are synchronized before EMA update
                    self.accelerator.wait_for_everyone()
                    self.ema_model.update(self.accelerator.unwrap_model(self.model))
                    self.global_step += 1
            
            # Gather metrics from all processes
            gathered_loss = self.accelerator.gather(loss.detach()).mean()
            total_loss += gathered_loss.item()
            num_batches += 1
            
            # Update progress bar
            if progress_bar is not None:
                progress_bar.update(1)
                progress_bar.set_postfix({
                    'loss': f'{gathered_loss.item():.4f}',
                    'lr': f'{self.lr_scheduler.get_last_lr()[0]:.2e}',
                    'step': self.global_step,
                    'ema_decay': f'{self.ema_model.get_decay():.4f}'
                })
        
        if progress_bar is not None:
            progress_bar.close()
        
        return total_loss / num_batches
    
    @torch.no_grad()
    def validate(self):
        """Validation with proper metric gathering"""
        if self.val_dataloader is None:
            return 0.0
        
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        for batch in self.val_dataloader:
            clean_images = batch
            batch_size = clean_images.shape[0]
            
            # Sample timesteps
            timesteps = torch.randint(
                0, self.noise_scheduler.config.num_train_timesteps,
                (batch_size,), device=clean_images.device
            ).long()
            
            # Add noise
            noise = torch.randn_like(clean_images)
            noisy_images = self.noise_scheduler.add_noise(clean_images, noise, timesteps)
            
            # Predict
            with self.accelerator.autocast():
                model_output = self.model(noisy_images, timesteps)
                predicted_noise = model_output.sample
                
                if hasattr(self.model.module, 'loss_fn'):
                    loss = self.model.module.loss_fn(predicted_noise, noise)
                else:
                    loss = F.mse_loss(predicted_noise, noise)
            
            # Gather loss from all processes
            gathered_loss = self.accelerator.gather(loss)
            total_loss += gathered_loss.mean().item()
            num_batches += 1
        
        self.model.train()
        return total_loss / num_batches
    
    @torch.no_grad()
    def sample_images(self, num_samples=4, num_inference_steps=None):
        """Generate samples with proper synchronization"""
        # Ensure all processes are synchronized
        self.accelerator.wait_for_everyone()
        
        # Use EMA model for inference
        ema_model = self.accelerator.unwrap_model(self.model)
        self.ema_model.copy_to(ema_model)
        ema_model.eval()
        
        # Use configured inference steps
        if num_inference_steps is None:
            num_inference_steps = self.config.num_inference_steps
        
        # Start with random noise
        shape = (num_samples, self.config.in_channels, 
                self.config.image_size, self.config.image_size)
        images = torch.randn(shape, device=self.accelerator.device)
        
        # Set timesteps for inference
        self.noise_scheduler.set_timesteps(num_inference_steps)
        
        # Denoising loop
        progress_bar = self.noise_scheduler.timesteps
        if self.accelerator.is_main_process:
            progress_bar = tqdm(
                self.noise_scheduler.timesteps,
                desc="Sampling",
                leave=False
            )
        
        for t in progress_bar:
            # Expand timestep to batch dimension
            timestep = torch.full((num_samples,), t, device=self.accelerator.device, dtype=torch.long)
            
            # Predict noise
            with self.accelerator.autocast():
                model_output = ema_model(images, timestep)
                noise_pred = model_output.sample
            
            # Denoise
            images = self.noise_scheduler.step(noise_pred, t, images).prev_sample
        
        # Denormalize to [0, 1]
        images = (images + 1) / 2
        images = torch.clamp(images, 0, 1)
        
        return images
    
    def save_checkpoint(self, checkpoint_dir):
        """Save checkpoint with proper synchronization"""
        self.accelerator.wait_for_everyone()
        
        if self.accelerator.is_main_process:
            os.makedirs(checkpoint_dir, exist_ok=True)
            
            # Save model
            unwrapped_model = self.accelerator.unwrap_model(self.model)
            torch.save(
                unwrapped_model.state_dict(),
                os.path.join(checkpoint_dir, "pytorch_model.bin")
            )
            
            # Save EMA
            torch.save(
                self.ema_model.state_dict(),
                os.path.join(checkpoint_dir, "ema_model.bin")
            )
            
            # Save training state
            training_state = {
                'epoch': self.epoch,
                'global_step': self.global_step,
                'config': self.config.__dict__,
            }
            torch.save(
                training_state,
                os.path.join(checkpoint_dir, "training_state.bin")
            )
            
            # Save optimizer and scheduler
            torch.save(
                self.optimizer.state_dict(),
                os.path.join(checkpoint_dir, "optimizer.bin")
            )
            torch.save(
                self.lr_scheduler.state_dict(),
                os.path.join(checkpoint_dir, "scheduler.bin")
            )
    
    def load_checkpoint(self, checkpoint_dir):
        """Load checkpoint with proper handling"""
        # Load model
        model_path = os.path.join(checkpoint_dir, "pytorch_model.bin")
        if os.path.exists(model_path):
            unwrapped_model = self.accelerator.unwrap_model(self.model)
            state_dict = torch.load(model_path, map_location=self.accelerator.device)
            unwrapped_model.load_state_dict(state_dict)
        
        # Load EMA
        ema_path = os.path.join(checkpoint_dir, "ema_model.bin")
        if os.path.exists(ema_path):
            ema_state = torch.load(ema_path, map_location=self.accelerator.device)
            self.ema_model.load_state_dict(ema_state)
        
        # Load training state
        state_path = os.path.join(checkpoint_dir, "training_state.bin")
        if os.path.exists(state_path):
            state = torch.load(state_path, map_location=self.accelerator.device)
            self.epoch = state['epoch']
            self.global_step = state['global_step']
        
        # Load optimizer
        opt_path = os.path.join(checkpoint_dir, "optimizer.bin")
        if os.path.exists(opt_path):
            opt_state = torch.load(opt_path, map_location=self.accelerator.device)
            self.optimizer.load_state_dict(opt_state)
        
        # Load scheduler
        sched_path = os.path.join(checkpoint_dir, "scheduler.bin")
        if os.path.exists(sched_path):
            sched_state = torch.load(sched_path, map_location=self.accelerator.device)
            self.lr_scheduler.load_state_dict(sched_state)
    
    def train(self):
        """Main training loop with all fixes"""
        self.accelerator.print("Starting training...")
        self.accelerator.print(f"Training on {self.accelerator.num_processes} processes")
        
        # Check for latest checkpoint
        checkpoint_latest = os.path.join(self.config.output_dir, "checkpoint_latest")
        if os.path.exists(checkpoint_latest):
            self.accelerator.print(f"Resuming from {checkpoint_latest}")
            self.load_checkpoint(checkpoint_latest)
        
        # Training loop
        for epoch in range(self.epoch, self.config.num_epochs):
            self.epoch = epoch
            
            # Train epoch
            train_loss = self.train_epoch()
            
            # Wait for all processes
            self.accelerator.wait_for_everyone()
            
            # Validation and logging
            if epoch % self.config.eval_every == 0:
                val_loss = self.validate()
                
                if self.accelerator.is_main_process:
                    self.logger.info(
                        f"Epoch {epoch}: Train Loss = {train_loss:.4f}, "
                        f"Val Loss = {val_loss:.4f}, "
                        f"LR = {self.lr_scheduler.get_last_lr()[0]:.2e}"
                    )
                    
                    # Generate samples
                    sample_images = self.sample_images(num_samples=4)
                    
                    # Save samples
                    sample_dir = os.path.join(self.config.output_dir, "samples")
                    os.makedirs(sample_dir, exist_ok=True)
                    
                    import torchvision.utils as vutils
                    vutils.save_image(
                        sample_images,
                        os.path.join(sample_dir, f"samples_epoch_{epoch:04d}.png"),
                        nrow=2,
                        normalize=False
                    )
            
            # Save checkpoint
            if epoch % self.config.save_every == 0:
                # Save epoch checkpoint
                checkpoint_path = os.path.join(
                    self.config.output_dir,
                    f"checkpoint_epoch_{epoch:04d}"
                )
                self.save_checkpoint(checkpoint_path)
                
                # Update latest checkpoint
                latest_path = os.path.join(self.config.output_dir, "checkpoint_latest")
                self.save_checkpoint(latest_path)
                
                if self.accelerator.is_main_process:
                    self.logger.info(f"Checkpoint saved at epoch {epoch}")
        
        # Final save
        self.accelerator.wait_for_everyone()
        if self.accelerator.is_main_process:
            self.logger.info("Training completed! Saving final model...")
            
            # Save final model with EMA weights
            final_path = os.path.join(self.config.output_dir, "final_model")
            os.makedirs(final_path, exist_ok=True)
            
            unwrapped_model = self.accelerator.unwrap_model(self.model)
            self.ema_model.copy_to(unwrapped_model)
            
            # Save model and config
            torch.save(
                unwrapped_model.state_dict(),
                os.path.join(final_path, "pytorch_model.bin")
            )
            
            with open(os.path.join(final_path, "config.json"), "w") as f:
                json.dump(self.config.__dict__, f, indent=2)
            
            self.logger.info(f"Final model saved to {final_path}")


# ═══════════════════════════════════════════════════════════════════
# MAIN SCRIPT AND UTILITIES
# ═══════════════════════════════════════════════════════════════════

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Train Enhanced Fiber Diffusion Model with Accelerate"
    )
    
    parser.add_argument(
        "--train_data_dir",
        type=str,
        required=True,
        help="Path to training data directory",
    )
    parser.add_argument(
        "--val_data_dir",
        type=str,
        default=None,
        help="Path to validation data directory",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="./fiber_diffusion_outputs",
        help="Output directory for checkpoints and samples",
    )
    parser.add_argument(
        "--image_size",
        type=int,
        default=512,
        help="Image resolution",
    )
    parser.add_argument(
        "--train_batch_size",
        type=int,
        default=4,
        help="Training batch size per device",
    )
    parser.add_argument(
        "--eval_batch_size",
        type=int,
        default=4,
        help="Evaluation batch size per device",
    )
    parser.add_argument(
        "--num_epochs",
        type=int,
        default=100,
        help="Number of training epochs",
    )
    parser.add_argument(
        "--learning_rate",
        type=float,
        default=1e-4,
        help="Learning rate",
    )
    parser.add_argument(
        "--gradient_accumulation_steps",
        type=int,
        default=4,
        help="Number of gradient accumulation steps",
    )
    parser.add_argument(
        "--mixed_precision",
        type=str,
        default="fp16",
        choices=["no", "fp16", "bf16"],
        help="Mixed precision training",
    )
    parser.add_argument(
        "--num_inference_steps",
        type=int,
        default=50,
        help="Number of denoising steps for sampling",
    )
    parser.add_argument(
        "--use_structural_loss",
        action="store_true",
        help="Use structural loss instead of MSE",
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=42,
        help="Random seed",
    )
    
    return parser.parse_args()


def test_model():
    """Test the model with dummy data"""
    print("Testing Enhanced Fiber Diffusion Model (Fixed Version)...")
    print("=" * 60)
    
    # Create dummy config
    config = FiberDiffusionConfig()
    config.image_size = 256  # Smaller for testing
    
    # Create model
    print("Creating model...")
    model = EnhancedFiberUNet(
        sample_size=config.image_size,
        use_structural_loss=True
    )
    
    # Test forward pass
    batch_size = 2
    dummy_images = torch.randn(batch_size, 3, config.image_size, config.image_size)
    dummy_timesteps = torch.randint(0, 1000, (batch_size,))
    
    print("Testing forward pass...")
    with torch.no_grad():
        output = model(dummy_images, dummy_timesteps)
    
    print("Testing EMA...")
    ema = EMAModel(model, decay=0.9999)
    ema.update(model)
    
    print("Testing structural loss...")
    loss_fn = StructuralLoss()
    loss = loss_fn(output.sample, dummy_images)
    
    # Print results
    print("=" * 60)
    print("✅ TEST RESULTS:")
    print(f"  Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"  Input shape: {dummy_images.shape}")
    print(f"  Output shape: {output.sample.shape}")
    print(f"  Loss value: {loss.item():.4f}")
    print(f"  EMA decay: {ema.get_decay():.4f}")
    print("✅ All tests passed!")
    print("=" * 60)
    
    # Test fixes
    print("\n📋 FIXES APPLIED:")
    print("  ✅ Learnable directional filters (no gradient destruction)")
    print("  ✅ Dynamic channel calculation (no GroupNorm crashes)")
    print("  ✅ Multi-path fusion (reduced information bottleneck)")
    print("  ✅ Corrected EMA decay formula")
    print("  ✅ Structural loss for fiber preservation")
    print("  ✅ Proper distributed training synchronization")
    print("  ✅ Configurable inference timesteps")
    print("  ✅ Enhanced data augmentation for fibers")
    print("=" * 60)


def main():
    """Main training function"""
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize accelerator
    accelerator = Accelerator(
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        mixed_precision=args.mixed_precision,
        log_with=None,  # Can add tracking later
    )
    
    # Set seed
    set_seed(args.seed)
    
    # Create configuration
    config = FiberDiffusionConfig()
    config.train_batch_size = args.train_batch_size
    config.eval_batch_size = args.eval_batch_size
    config.num_epochs = args.num_epochs
    config.learning_rate = args.learning_rate
    config.gradient_accumulation_steps = args.gradient_accumulation_steps
    config.image_size = args.image_size
    config.output_dir = args.output_dir
    config.seed = args.seed
    config.mixed_precision = args.mixed_precision
    config.num_inference_steps = args.num_inference_steps
    config.use_structural_loss = args.use_structural_loss
    
    # Log configuration
    accelerator.print(f"Configuration:")
    accelerator.print(f"  Device: {accelerator.device}")
    accelerator.print(f"  Num processes: {accelerator.num_processes}")
    accelerator.print(f"  Mixed precision: {accelerator.mixed_precision}")
    for key, value in config.__dict__.items():
        accelerator.print(f"  {key}: {value}")
    
    # Create datasets
    train_dataset = FiberDataset(
        image_dir=args.train_data_dir,
        image_size=config.image_size,
        augment=True
    )
    
    val_dataset = None
    if args.val_data_dir:
        val_dataset = FiberDataset(
            image_dir=args.val_data_dir,
            image_size=config.image_size,
            augment=False
        )
    
    accelerator.print(f"\nDataset sizes:")
    accelerator.print(f"  Train: {len(train_dataset)}")
    if val_dataset:
        accelerator.print(f"  Val: {len(val_dataset)}")
    
    # Create dataloaders
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=config.train_batch_size,
        shuffle=True,
        num_workers=config.num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_dataloader = None
    if val_dataset:
        val_dataloader = DataLoader(
            val_dataset,
            batch_size=config.eval_batch_size,
            shuffle=False,
            num_workers=config.num_workers,
            pin_memory=True
        )
    
    # Create trainer
    trainer = AccelerateFiberDiffusionTrainer(
        config=config,
        accelerator=accelerator,
        train_dataloader=train_dataloader,
        val_dataloader=val_dataloader
    )
    
    # Start training
    trainer.train()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        # Run tests if no arguments
        test_model()
    else:
        # Run training
        main()